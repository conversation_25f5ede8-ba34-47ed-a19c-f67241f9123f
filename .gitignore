# Rust build artifacts
/target/
Cargo.lock

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.bak
*.backup
*.orig

# Log files
*.log

# Local configuration
.env
.env.local

# References folder (contains existing implementation)
references/

# Build outputs
*.so
*.dylib
*.dll

# Test artifacts
test-output/
*.test

# Documentation build
docs/_build/

##
.jj
.git