/* Virtual Desktop CFFI Test Styling - Simple Version */

/* Waybar Window */
window#waybar {
    background-color: transparent;
    color: #e6e1e9;
    font-family: "JetBrainsMono Nerd Font", monospace;
    font-size: 14px;
    font-weight: 500;
}

/* Center modules container */
.modules-center {
    padding: 7px;
    margin: 10px 0 5px 0;
    border-radius: 10px;
    background: rgba(20, 19, 24, 0.6);
    box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.6);
}

/* Virtual Desktop CFFI Module Container - styling handled by individual labels */

/* Individual Virtual Desktop Labels */
#cffi-virtual_desktops label {
    padding: 0px 8px;
    margin: 0 2px;
    transition: all 0.15s ease;
    border-radius: 4px;
    min-width: 0;
}

/* Hidden virtual desktops (smooth disappearance) */
#cffi-virtual_desktops label.hidden {
    padding: 0;
    margin: 0;
    opacity: 0;
    transition: all 0.15s ease;
}

/* Focused virtual desktop */
#cffi-virtual_desktops label.vdesk-focused {
    color: #cdbdff;
    font-weight: bold;
    text-shadow: 0px 0px 2px rgba(205, 189, 255, 0.3);
    background: rgba(205, 189, 255, 0.1);
}

/* Unfocused virtual desktop */
#cffi-virtual_desktops label.vdesk-unfocused {
    color: rgba(230, 225, 233, 0.7);
    transition: all 0.15s ease;
}

/* Hover effects for unfocused desktops */
#cffi-virtual_desktops label.vdesk-unfocused:hover {
    color: #cdbdff;
    background: rgba(205, 189, 255, 0.05);
    transition: all 0.15s ease;
}

/* Tooltip styling */
tooltip {
    background: #201f24;
    color: #e6e1e9;
    border-radius: 8px;
    border: 1px solid rgba(205, 189, 255, 0.2);
    font-size: 12px;
    padding: 8px;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.6);
}