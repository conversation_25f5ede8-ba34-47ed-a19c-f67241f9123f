{"layer": "top", "position": "top", "mod": "dock", "exclusive": true, "passthrough": false, "gtk-layer-shell": true, "height": 40, "modules-left": [], "modules-center": ["cffi/virtual_desktops"], "modules-right": [], "cffi/virtual_desktops": {"module_path": "/home/<USER>/Code/Vd_waybar/target/release/libwaybar_virtual_desktops_cffi.so", "config": {"format": "{icon} {name}", "format_icons": {"1": "󰋇", "2": "󰍉", "3": "󰎄", "4": "󰕧", "5": "󰈹"}, "show_empty": false, "show_window_count": false, "separator": "", "sort_by": "number"}}}